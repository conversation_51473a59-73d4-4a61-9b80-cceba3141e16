// 主JavaScript文件 - 生日贺卡交互功能
// 为董双拿女士的农历生日贺卡提供完整的交互体验

class BirthdayCard {
    constructor() {
        this.isExpanded = false;
        this.animationsEnabled = true;
        this.surpriseCards = new Map();
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupInitialState();
        this.setupSurpriseCards();
        this.handlePageLoad();
    }

    // 绑定事件监听器
    bindEvents() {
        // 贺卡点击事件
        const card = document.getElementById('birthdayCard');
        if (card) {
            card.addEventListener('click', () => this.toggleCard());
            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.toggleCard();
                }
            });
        }

        // 动画控制按钮
        const animationBtn = document.getElementById('animationBtn');
        if (animationBtn) {
            animationBtn.addEventListener('click', () => this.toggleAnimations());
        }

        // 键盘导航支持
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));

        // 窗口大小变化时重新计算布局
        window.addEventListener('resize', () => this.handleResize());

        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());
    }

    // 设置惊喜卡片
    setupSurpriseCards() {
        const surpriseCards = document.querySelectorAll('.surprise-card');
        surpriseCards.forEach(card => {
            const cardId = card.id;
            this.surpriseCards.set(cardId, false); // false = 未翻转

            card.addEventListener('click', () => this.flipSurpriseCard(cardId));
            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.flipSurpriseCard(cardId);
                }
            });

            // 设置可访问性属性
            card.setAttribute('tabindex', '0');
            card.setAttribute('role', 'button');
            card.setAttribute('aria-label', '点击查看惊喜祝福');
        });
    }

    // 翻转惊喜卡片
    flipSurpriseCard(cardId) {
        const card = document.getElementById(cardId);
        if (!card) return;

        const isFlipped = this.surpriseCards.get(cardId);

        if (isFlipped) {
            // 翻回正面
            card.classList.remove('flipped');
            this.surpriseCards.set(cardId, false);
            card.setAttribute('aria-label', '点击查看惊喜祝福');
        } else {
            // 翻到背面
            card.classList.add('flipped');
            this.surpriseCards.set(cardId, true);
            card.setAttribute('aria-label', '点击返回卡片正面');

            // 添加翻转音效和动画
            this.addFlipEffect(card);

            // 触发庆祝动画
            if (window.animationController) {
                window.animationController.createSpecialEffect('click',
                    card.offsetLeft + card.offsetWidth / 2,
                    card.offsetTop + card.offsetHeight / 2);
            }
        }
    }

    // 添加翻转效果
    addFlipEffect(card) {
        // 添加闪光效果
        const sparkle = document.createElement('div');
        sparkle.className = 'flip-sparkle';
        sparkle.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, #FFD700, transparent);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: sparkleExpand 0.8s ease-out forwards;
            pointer-events: none;
            z-index: 100;
        `;

        card.style.position = 'relative';
        card.appendChild(sparkle);

        setTimeout(() => {
            if (sparkle.parentNode) {
                sparkle.parentNode.removeChild(sparkle);
            }
        }, 800);
    }

    // 设置初始状态
    setupInitialState() {
        // 隐藏详细祝福内容
        const detailedBlessing = document.getElementById('detailedBlessing');
        if (detailedBlessing) {
            detailedBlessing.classList.remove('expanded');
        }

        // 设置初始动画状态
        this.updateAnimationState();

        // 设置ARIA属性
        this.updateAriaAttributes();
    }

    // 处理页面加载
    handlePageLoad() {
        // 延迟隐藏加载覆盖层
        setTimeout(() => {
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.classList.add('hidden');
                // 完全移除元素以释放内存
                setTimeout(() => {
                    if (loadingOverlay.parentNode) {
                        loadingOverlay.parentNode.removeChild(loadingOverlay);
                    }
                }, 500);
            }
        }, 2000);

        // 添加页面加载动画类
        document.body.classList.add('loaded');

        // 启动文字打字机效果
        this.startTypewriterEffect();
    }

    // 切换贺卡展开状态
    toggleCard() {
        const detailedBlessing = document.getElementById('detailedBlessing');
        const clickHint = document.getElementById('clickHint');
        const card = document.getElementById('birthdayCard');

        if (!detailedBlessing) return;

        this.isExpanded = !this.isExpanded;

        if (this.isExpanded) {
            // 展开详细祝福
            detailedBlessing.classList.add('expanded');
            if (clickHint) clickHint.classList.add('hidden');
            
            // 添加展开动画效果
            card?.classList.add('expanded');
            
            // 播放展开音效（如果有）
            this.playSound('expand');
            
        } else {
            // 收起详细祝福
            detailedBlessing.classList.remove('expanded');
            if (clickHint) clickHint.classList.remove('hidden');
            
            // 移除展开状态
            card?.classList.remove('expanded');
        }

        // 更新ARIA属性
        this.updateAriaAttributes();

        // 添加交互反馈动画
        this.addInteractionFeedback(card);
    }

    // 切换动画状态
    toggleAnimations() {
        this.animationsEnabled = !this.animationsEnabled;
        this.updateAnimationState();
        
        const animationBtn = document.getElementById('animationBtn');
        const animationText = animationBtn?.querySelector('.animation-text');
        
        if (animationText) {
            animationText.textContent = this.animationsEnabled ? '暂停动画' : '恢复动画';
        }

        // 保存用户偏好
        localStorage.setItem('birthdayCard_animationsEnabled', this.animationsEnabled);
    }

    // 更新动画状态
    updateAnimationState() {
        const body = document.body;
        if (this.animationsEnabled) {
            body.classList.remove('animations-paused');
            body.classList.add('animations-running');
        } else {
            body.classList.remove('animations-running');
            body.classList.add('animations-paused');
        }
    }

    // 键盘事件处理
    handleKeyboard(e) {
        switch(e.key) {
            case 'Escape':
                if (this.isExpanded) {
                    this.toggleCard();
                }
                // 关闭所有翻转的惊喜卡片
                this.surpriseCards.forEach((isFlipped, cardId) => {
                    if (isFlipped) {
                        this.flipSurpriseCard(cardId);
                    }
                });
                break;
            case 'a':
            case 'A':
                if (e.ctrlKey || e.metaKey) return; // 避免干扰全选
                this.toggleAnimations();
                break;
            case '1':
            case '2':
            case '3':
                // 数字键快速翻转对应的惊喜卡片
                const cardId = `surpriseCard${e.key}`;
                if (this.surpriseCards.has(cardId)) {
                    this.flipSurpriseCard(cardId);
                }
                break;
        }
    }

    // 窗口大小变化处理
    handleResize() {
        // 重新计算动画位置（如果需要）
        this.recalculateAnimations();
    }

    // 页面可见性变化处理
    handleVisibilityChange() {
        if (document.hidden) {
            // 页面隐藏时暂停动画以节省资源
            document.body.classList.add('animations-paused');
        } else {
            // 页面显示时恢复动画状态
            this.updateAnimationState();
        }
    }

    // 文字打字机效果
    startTypewriterEffect() {
        const blessingText = document.querySelector('.blessing-main');
        if (!blessingText) return;

        // 添加打字机效果类
        blessingText.classList.add('typewriter-text');
        
        // 延迟添加发光效果
        setTimeout(() => {
            const nameHighlight = document.querySelector('.name-highlight');
            if (nameHighlight) {
                nameHighlight.classList.add('glow-text');
            }
        }, 3000);
    }

    // 更新ARIA属性
    updateAriaAttributes() {
        const card = document.getElementById('birthdayCard');
        if (card) {
            card.setAttribute('aria-expanded', this.isExpanded);
            card.setAttribute('aria-label', 
                this.isExpanded ? '点击收起详细祝福' : '点击展开更多祝福'
            );
        }
    }

    // 添加交互反馈动画
    addInteractionFeedback(element) {
        if (!element) return;
        
        element.classList.add('heartbeat');
        setTimeout(() => {
            element.classList.remove('heartbeat');
        }, 600);
    }

    // 重新计算动画
    recalculateAnimations() {
        // 根据新的窗口大小调整动画参数
        const decorations = document.querySelectorAll('.petal, .star, .butterfly');
        decorations.forEach(decoration => {
            // 重新设置动画延迟和持续时间
            const animationDelay = Math.random() * 5;
            decoration.style.animationDelay = `${animationDelay}s`;
        });
    }

    // 播放音效
    playSound(type) {
        // 这里可以添加音效播放逻辑
        // 例如：点击音效、展开音效等
        console.log(`Playing sound: ${type}`);
    }

    // 获取用户偏好设置
    loadUserPreferences() {
        const savedAnimationState = localStorage.getItem('birthdayCard_animationsEnabled');
        if (savedAnimationState !== null) {
            this.animationsEnabled = savedAnimationState === 'true';
            this.updateAnimationState();
        }
    }

    // 错误处理
    handleError(error, context) {
        console.error(`Birthday Card Error in ${context}:`, error);
        // 可以添加用户友好的错误提示
    }
}

// 工具函数
const utils = {
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // 检测设备类型
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    // 检测浏览器支持
    supportsWebAudio() {
        return !!(window.AudioContext || window.webkitAudioContext);
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    try {
        // 创建生日贺卡实例
        window.birthdayCard = new BirthdayCard();
        
        // 加载用户偏好
        window.birthdayCard.loadUserPreferences();
        
        console.log('🎂 生日贺卡已成功加载！');
    } catch (error) {
        console.error('生日贺卡初始化失败:', error);
    }
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { BirthdayCard, utils };
}
