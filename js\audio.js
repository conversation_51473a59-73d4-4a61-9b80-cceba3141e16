// 音频控制模块 - 为生日贺卡提供音乐功能
// 为董双拿女士的生日贺卡提供温馨的音乐体验

class AudioController {
    constructor() {
        this.audio = null;
        this.isPlaying = false;
        this.volume = 0.5;
        this.isMuted = false;
        this.audioContext = null;
        this.gainNode = null;
        this.init();
    }

    init() {
        this.setupAudioElement();
        this.setupAudioContext();
        this.bindEvents();
        this.loadUserPreferences();
        this.createFallbackMusic();
    }

    // 设置音频元素
    setupAudioElement() {
        this.audio = document.getElementById('backgroundMusic');
        if (!this.audio) {
            console.warn('音频元素未找到，创建备用音频元素');
            this.createAudioElement();
        }

        if (this.audio) {
            this.audio.volume = this.volume;
            this.audio.loop = true;
            this.audio.preload = 'auto';
            
            // 音频事件监听
            this.audio.addEventListener('loadstart', () => this.onAudioLoadStart());
            this.audio.addEventListener('canplay', () => this.onAudioCanPlay());
            this.audio.addEventListener('error', (e) => this.onAudioError(e));
            this.audio.addEventListener('ended', () => this.onAudioEnded());
            this.audio.addEventListener('timeupdate', () => this.onTimeUpdate());
        }
    }

    // 创建音频元素
    createAudioElement() {
        this.audio = document.createElement('audio');
        this.audio.id = 'backgroundMusic';
        this.audio.preload = 'auto';
        this.audio.loop = true;
        
        // 添加音频源
        const mp3Source = document.createElement('source');
        mp3Source.src = 'assets/audio/birthday-song.mp3';
        mp3Source.type = 'audio/mpeg';
        
        const oggSource = document.createElement('source');
        oggSource.src = 'assets/audio/birthday-song.ogg';
        oggSource.type = 'audio/ogg';
        
        this.audio.appendChild(mp3Source);
        this.audio.appendChild(oggSource);
        document.body.appendChild(this.audio);
    }

    // 设置Web Audio API上下文
    setupAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            if (this.audio && this.audioContext) {
                const source = this.audioContext.createMediaElementSource(this.audio);
                this.gainNode = this.audioContext.createGain();
                
                source.connect(this.gainNode);
                this.gainNode.connect(this.audioContext.destination);
                
                this.gainNode.gain.value = this.volume;
            }
        } catch (error) {
            console.warn('Web Audio API 不支持，使用基础音频控制:', error);
        }
    }

    // 绑定事件
    bindEvents() {
        // 音乐控制按钮
        const musicBtn = document.getElementById('musicBtn');
        if (musicBtn) {
            musicBtn.addEventListener('click', () => this.toggleMusic());
        }

        // 音量控制滑块
        const volumeSlider = document.getElementById('volumeSlider');
        if (volumeSlider) {
            volumeSlider.addEventListener('input', (e) => this.setVolume(e.target.value / 100));
            volumeSlider.value = this.volume * 100;
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => this.handleVisibilityChange());

        // 用户交互后启用音频上下文
        document.addEventListener('click', () => this.resumeAudioContext(), { once: true });
        document.addEventListener('touchstart', () => this.resumeAudioContext(), { once: true });
    }

    // 切换音乐播放状态
    async toggleMusic() {
        if (!this.audio) {
            console.error('音频元素不可用');
            return;
        }

        try {
            if (this.isPlaying) {
                await this.pauseMusic();
            } else {
                await this.playMusic();
            }
        } catch (error) {
            console.error('音乐控制失败:', error);
            this.showAudioError('音乐播放失败，请检查音频文件');
        }
    }

    // 播放音乐
    async playMusic() {
        if (!this.audio) return;

        try {
            // 恢复音频上下文
            await this.resumeAudioContext();
            
            await this.audio.play();
            this.isPlaying = true;
            this.updateMusicButton();
            
            // 淡入效果
            this.fadeIn();
            
            console.log('🎵 音乐开始播放');
        } catch (error) {
            console.error('播放音乐失败:', error);
            
            if (error.name === 'NotAllowedError') {
                this.showAudioError('浏览器阻止了自动播放，请点击播放按钮');
            } else {
                this.showAudioError('音乐文件加载失败');
            }
        }
    }

    // 暂停音乐
    async pauseMusic() {
        if (!this.audio) return;

        try {
            // 淡出效果
            await this.fadeOut();
            
            this.audio.pause();
            this.isPlaying = false;
            this.updateMusicButton();
            
            console.log('🎵 音乐已暂停');
        } catch (error) {
            console.error('暂停音乐失败:', error);
        }
    }

    // 设置音量
    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        
        if (this.audio) {
            this.audio.volume = this.volume;
        }
        
        if (this.gainNode) {
            this.gainNode.gain.value = this.volume;
        }
        
        // 保存用户偏好
        localStorage.setItem('birthdayCard_volume', this.volume);
        
        // 更新音量显示
        this.updateVolumeDisplay();
    }

    // 静音切换
    toggleMute() {
        this.isMuted = !this.isMuted;
        
        if (this.audio) {
            this.audio.muted = this.isMuted;
        }
        
        this.updateMusicButton();
    }

    // 淡入效果
    fadeIn(duration = 1000) {
        if (!this.audio) return;
        
        const startVolume = 0;
        const endVolume = this.volume;
        const steps = 50;
        const stepTime = duration / steps;
        const volumeStep = (endVolume - startVolume) / steps;
        
        let currentStep = 0;
        this.audio.volume = startVolume;
        
        const fadeInterval = setInterval(() => {
            currentStep++;
            const newVolume = startVolume + (volumeStep * currentStep);
            this.audio.volume = Math.min(newVolume, endVolume);
            
            if (currentStep >= steps) {
                clearInterval(fadeInterval);
                this.audio.volume = endVolume;
            }
        }, stepTime);
    }

    // 淡出效果
    fadeOut(duration = 500) {
        return new Promise((resolve) => {
            if (!this.audio) {
                resolve();
                return;
            }
            
            const startVolume = this.audio.volume;
            const steps = 25;
            const stepTime = duration / steps;
            const volumeStep = startVolume / steps;
            
            let currentStep = 0;
            
            const fadeInterval = setInterval(() => {
                currentStep++;
                const newVolume = startVolume - (volumeStep * currentStep);
                this.audio.volume = Math.max(newVolume, 0);
                
                if (currentStep >= steps) {
                    clearInterval(fadeInterval);
                    this.audio.volume = 0;
                    resolve();
                }
            }, stepTime);
        });
    }

    // 更新音乐按钮状态
    updateMusicButton() {
        const musicBtn = document.getElementById('musicBtn');
        const musicIcon = musicBtn?.querySelector('.music-icon');
        const musicText = musicBtn?.querySelector('.music-text');
        
        if (musicIcon && musicText) {
            if (this.isPlaying) {
                musicIcon.textContent = '🎵';
                musicText.textContent = '暂停音乐';
                musicBtn.classList.add('playing');
            } else {
                musicIcon.textContent = this.isMuted ? '🔇' : '🎵';
                musicText.textContent = '播放音乐';
                musicBtn.classList.remove('playing');
            }
        }
    }

    // 更新音量显示
    updateVolumeDisplay() {
        const volumeSlider = document.getElementById('volumeSlider');
        if (volumeSlider) {
            volumeSlider.value = this.volume * 100;
        }
    }

    // 恢复音频上下文
    async resumeAudioContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            try {
                await this.audioContext.resume();
                console.log('音频上下文已恢复');
            } catch (error) {
                console.error('恢复音频上下文失败:', error);
            }
        }
    }

    // 键盘事件处理
    handleKeyboard(e) {
        switch(e.key) {
            case ' ':
                if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'BUTTON') {
                    e.preventDefault();
                    this.toggleMusic();
                }
                break;
            case 'm':
            case 'M':
                this.toggleMute();
                break;
            case 'ArrowUp':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.setVolume(this.volume + 0.1);
                }
                break;
            case 'ArrowDown':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.setVolume(this.volume - 0.1);
                }
                break;
        }
    }

    // 页面可见性变化处理
    handleVisibilityChange() {
        if (document.hidden && this.isPlaying) {
            // 页面隐藏时降低音量
            this.setVolume(this.volume * 0.3);
        } else if (!document.hidden && this.isPlaying) {
            // 页面显示时恢复音量
            this.setVolume(this.volume / 0.3);
        }
    }

    // 音频事件处理
    onAudioLoadStart() {
        console.log('开始加载音频文件...');
    }

    onAudioCanPlay() {
        console.log('音频文件可以播放');
    }

    onAudioError(e) {
        console.error('音频加载错误:', e);
        this.showAudioError('音频文件加载失败，将使用静音模式');
    }

    onAudioEnded() {
        console.log('音频播放结束');
        this.isPlaying = false;
        this.updateMusicButton();
    }

    onTimeUpdate() {
        // 可以在这里添加进度条更新逻辑
    }

    // 显示音频错误
    showAudioError(message) {
        console.warn('音频错误:', message);
        // 可以添加用户友好的错误提示
    }

    // 创建备用音乐（使用Web Audio API生成简单旋律）
    createFallbackMusic() {
        if (!this.audioContext) return;

        // 创建简单的生日歌旋律
        const notes = [
            { freq: 261.63, duration: 0.5 }, // C4
            { freq: 261.63, duration: 0.5 }, // C4
            { freq: 293.66, duration: 1.0 }, // D4
            { freq: 261.63, duration: 1.0 }, // C4
            { freq: 349.23, duration: 1.0 }, // F4
            { freq: 329.63, duration: 2.0 }  // E4
        ];

        this.fallbackMelody = notes;
    }

    // 播放备用旋律
    playFallbackMelody() {
        if (!this.audioContext || !this.fallbackMelody) return;

        let currentTime = this.audioContext.currentTime;
        
        this.fallbackMelody.forEach(note => {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            oscillator.frequency.value = note.freq;
            oscillator.type = 'sine';
            
            gainNode.gain.setValueAtTime(0, currentTime);
            gainNode.gain.linearRampToValueAtTime(0.1, currentTime + 0.1);
            gainNode.gain.linearRampToValueAtTime(0, currentTime + note.duration);
            
            oscillator.start(currentTime);
            oscillator.stop(currentTime + note.duration);
            
            currentTime += note.duration;
        });
    }

    // 加载用户偏好
    loadUserPreferences() {
        const savedVolume = localStorage.getItem('birthdayCard_volume');
        if (savedVolume !== null) {
            this.setVolume(parseFloat(savedVolume));
        }
    }

    // 清理资源
    cleanup() {
        if (this.audio) {
            this.audio.pause();
            this.audio.src = '';
        }
        
        if (this.audioContext) {
            this.audioContext.close();
        }
    }
}

// 页面加载完成后初始化音频控制器
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.audioController = new AudioController();
        console.log('🎵 音频控制器已成功加载！');
    } catch (error) {
        console.error('音频控制器初始化失败:', error);
    }
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.audioController) {
        window.audioController.cleanup();
    }
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AudioController;
}
