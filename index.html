<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生日快乐 - 董双拿女士</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Ma+Shan+Zheng&family=Noto+Serif+SC:wght@400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 装饰元素容器 -->
    <div class="decorations">
        <!-- 飘落的花瓣 -->
        <div class="petals">
            <div class="petal petal-1">🌸</div>
            <div class="petal petal-2">🌺</div>
            <div class="petal petal-3">🌸</div>
            <div class="petal petal-4">🌺</div>
            <div class="petal petal-5">🌸</div>
            <div class="petal petal-6">🌺</div>
        </div>
        
        <!-- 闪烁的星星 -->
        <div class="stars">
            <div class="star star-1">✨</div>
            <div class="star star-2">⭐</div>
            <div class="star star-3">✨</div>
            <div class="star star-4">⭐</div>
            <div class="star star-5">✨</div>
        </div>
        
        <!-- 飞舞的蝴蝶 -->
        <div class="butterflies">
            <div class="butterfly butterfly-1">🦋</div>
            <div class="butterfly butterfly-2">🦋</div>
        </div>
    </div>

    <!-- 主容器 -->
    <main class="main-container">
        <!-- 生日贺卡 -->
        <article class="birthday-card" id="birthdayCard" tabindex="0" role="button" aria-label="点击展开生日祝福">
            <header class="card-header">
                <h1 class="main-title">
                    <span class="title-line">生日快乐</span>
                    <span class="name-highlight">董双拿女士</span>
                </h1>
            </header>
            
            <section class="card-content">
                <div class="blessing-text" id="blessingText">
                    <p class="blessing-main">
                        在这个特别的日子里，<br>
                        愿您的每一天都充满阳光与欢笑，<br>
                        愿您的生活如花朵般绚烂美丽！
                    </p>
                    <div class="lunar-date">
                        <span class="date-label">今日</span>
                        <span class="date-value">农历六月十二</span>
                    </div>
                </div>

                <!-- 展开后的详细祝福 -->
                <div class="detailed-blessing" id="detailedBlessing">
                    <p class="blessing-detail">
                        亲爱的董双拿女士，<br><br>

                        今天是您的生日，这是一个充满意义的美好日子。<br>
                        在这个特殊的时刻，送上最真挚的祝福。<br><br>

                        在这个特殊的时刻，我想对您说：<br><br>

                        愿您身体健康，笑容常在，<br>
                        愿您心想事成，幸福满怀，<br>
                        愿您的人生如春天般温暖，<br>
                        如夏天般热烈，如秋天般丰收，<br>
                        如冬天般宁静而美好。<br><br>

                        愿您福如东海长流水，<br>
                        寿比南山不老松，<br>
                        愿时光温柔以待，<br>
                        愿岁月不老，您永远年轻！<br><br>

                        <strong>生日快乐，董双拿女士！🎂🎉</strong><br>
                        <em>愿您年年有今日，岁岁有今朝！</em>
                    </p>
                </div>
            </section>
            
            <footer class="card-footer">
                <div class="click-hint" id="clickHint">
                    <span>💝 点击贺卡查看更多祝福 💝</span>
                </div>
            </footer>
        </article>
        
        <!-- 控制面板 -->
        <aside class="control-panel">
            <!-- 动画控制 -->
            <div class="animation-controls">
                <button class="animation-btn" id="animationBtn" aria-label="暂停/恢复动画">
                    <span class="animation-icon">✨</span>
                    <span class="animation-text">暂停动画</span>
                </button>
            </div>
        </aside>

        <!-- 惊喜卡片区域 -->
        <section class="surprise-cards">
            <div class="surprise-card" id="surpriseCard1" data-surprise="1">
                <div class="card-front">
                    <span class="surprise-icon">🌸</span>
                    <span class="surprise-text">点击小惊喜</span>
                </div>
                <div class="card-back">
                    <h4>健康长寿</h4>
                    <p>愿您身体健康，<br>心情愉悦，<br>长寿如松柏，<br>幸福如蜜糖！</p>
                </div>
            </div>

            <div class="surprise-card" id="surpriseCard2" data-surprise="2">
                <div class="card-front">
                    <span class="surprise-icon">💝</span>
                    <span class="surprise-text">点击小惊喜</span>
                </div>
                <div class="card-back">
                    <h4>家庭幸福</h4>
                    <p>愿您家庭和睦，<br>子女孝顺，<br>生活充满<br>欢声笑语！</p>
                </div>
            </div>

            <div class="surprise-card" id="surpriseCard3" data-surprise="3">
                <div class="card-front">
                    <span class="surprise-icon">🎂</span>
                    <span class="surprise-text">点击小惊喜</span>
                </div>
                <div class="card-back">
                    <h4>生日快乐</h4>
                    <p>董双拿女士，<br>生日快乐！<br>年年有今日，<br>岁岁有今朝！🎉</p>
                </div>
            </div>
        </section>
    </main>
    

    
    <!-- 加载提示 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner">🎂</div>
            <p>正在准备生日惊喜...</p>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
</body>
</html>
