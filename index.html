<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生日快乐 - 董双拿女士</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Ma+Shan+Zheng&family=Noto+Serif+SC:wght@400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 装饰元素容器 -->
    <div class="decorations">
        <!-- 飘落的花瓣 -->
        <div class="petals">
            <div class="petal petal-1">🌸</div>
            <div class="petal petal-2">🌺</div>
            <div class="petal petal-3">🌸</div>
            <div class="petal petal-4">🌺</div>
            <div class="petal petal-5">🌸</div>
            <div class="petal petal-6">🌺</div>
        </div>
        
        <!-- 闪烁的星星 -->
        <div class="stars">
            <div class="star star-1">✨</div>
            <div class="star star-2">⭐</div>
            <div class="star star-3">✨</div>
            <div class="star star-4">⭐</div>
            <div class="star star-5">✨</div>
        </div>
        
        <!-- 飞舞的蝴蝶 -->
        <div class="butterflies">
            <div class="butterfly butterfly-1">🦋</div>
            <div class="butterfly butterfly-2">🦋</div>
        </div>
    </div>

    <!-- 主容器 -->
    <main class="main-container">
        <!-- 生日贺卡 -->
        <article class="birthday-card" id="birthdayCard" tabindex="0" role="button" aria-label="点击展开生日祝福">
            <header class="card-header">
                <h1 class="main-title">
                    <span class="title-line">生日快乐</span>
                    <span class="name-highlight">董双拿女士</span>
                </h1>
            </header>
            
            <section class="card-content">
                <div class="blessing-text" id="blessingText">
                    <p class="blessing-main">
                        在这个特别的日子里，<br>
                        愿您的每一天都充满阳光与欢笑，<br>
                        愿您的生活如花朵般绚烂美丽！
                    </p>
                </div>
                
                <!-- 展开后的详细祝福 -->
                <div class="detailed-blessing" id="detailedBlessing">
                    <p class="blessing-detail">
                        亲爱的董双拿女士，<br><br>
                        
                        今天是您的生日，是一个值得庆祝的美好日子。<br>
                        在这个特殊的时刻，我想对您说：<br><br>
                        
                        愿您身体健康，笑容常在，<br>
                        愿您心想事成，幸福满怀，<br>
                        愿您的人生如春天般温暖，<br>
                        如夏天般热烈，如秋天般丰收，<br>
                        如冬天般宁静而美好。<br><br>
                        
                        愿时光温柔以待，<br>
                        愿岁月不老，您永远年轻！<br><br>
                        
                        <strong>生日快乐！🎂🎉</strong>
                    </p>
                </div>
            </section>
            
            <footer class="card-footer">
                <div class="click-hint" id="clickHint">
                    <span>💝 点击贺卡查看更多祝福 💝</span>
                </div>
            </footer>
        </article>
        
        <!-- 控制面板 -->
        <aside class="control-panel">
            <!-- 音乐控制 -->
            <div class="music-controls">
                <button class="music-btn" id="musicBtn" aria-label="播放/暂停音乐">
                    <span class="music-icon">🎵</span>
                    <span class="music-text">播放音乐</span>
                </button>
                <div class="volume-control">
                    <input type="range" id="volumeSlider" min="0" max="100" value="50" class="volume-slider" aria-label="音量控制">
                </div>
            </div>
            
            <!-- 动画控制 -->
            <div class="animation-controls">
                <button class="animation-btn" id="animationBtn" aria-label="暂停/恢复动画">
                    <span class="animation-icon">✨</span>
                    <span class="animation-text">暂停动画</span>
                </button>
            </div>
            
            <!-- 分享按钮 -->
            <div class="share-controls">
                <button class="share-btn" id="shareBtn" aria-label="分享生日贺卡">
                    <span class="share-icon">📤</span>
                    <span class="share-text">分享贺卡</span>
                </button>
            </div>
        </aside>
    </main>
    
    <!-- 音频元素 -->
    <audio id="backgroundMusic" preload="auto" loop>
        <source src="assets/audio/birthday-song.mp3" type="audio/mpeg">
        <source src="assets/audio/birthday-song.ogg" type="audio/ogg">
        您的浏览器不支持音频播放。
    </audio>
    
    <!-- 加载提示 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner">🎂</div>
            <p>正在准备生日惊喜...</p>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/audio.js"></script>
    <script src="js/share.js"></script>
</body>
</html>
