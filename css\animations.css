/* 动画效果样式文件 */

/* 页面加载动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 文字动画 */
@keyframes typewriter {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes glow {
    0%, 100% {
        text-shadow: 0 0 5px rgba(255, 215, 0, 0.5), 0 0 10px rgba(255, 215, 0, 0.3);
    }
    50% {
        text-shadow: 0 0 10px rgba(255, 215, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.5), 0 0 30px rgba(255, 215, 0, 0.3);
    }
}

/* 花瓣飘落动画 */
@keyframes petalFall1 {
    0% {
        transform: translateY(-100vh) translateX(0) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) translateX(100px) rotate(360deg);
        opacity: 0;
    }
}

@keyframes petalFall2 {
    0% {
        transform: translateY(-100vh) translateX(0) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) translateX(-80px) rotate(-360deg);
        opacity: 0;
    }
}

@keyframes petalFall3 {
    0% {
        transform: translateY(-100vh) translateX(0) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) translateX(150px) rotate(720deg);
        opacity: 0;
    }
}

/* 星星闪烁动画 */
@keyframes starTwinkle {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

@keyframes starGlow {
    0%, 100% {
        filter: brightness(1);
    }
    50% {
        filter: brightness(1.5) drop-shadow(0 0 10px rgba(255, 215, 0, 0.8));
    }
}

/* 蝴蝶飞舞动画 */
@keyframes butterflyFly1 {
    0% {
        transform: translateX(-50px) translateY(50vh);
    }
    25% {
        transform: translateX(25vw) translateY(30vh);
    }
    50% {
        transform: translateX(50vw) translateY(60vh);
    }
    75% {
        transform: translateX(75vw) translateY(20vh);
    }
    100% {
        transform: translateX(100vw) translateY(40vh);
    }
}

@keyframes butterflyFly2 {
    0% {
        transform: translateX(100vw) translateY(30vh);
    }
    25% {
        transform: translateX(75vw) translateY(70vh);
    }
    50% {
        transform: translateX(50vw) translateY(20vh);
    }
    75% {
        transform: translateX(25vw) translateY(80vh);
    }
    100% {
        transform: translateX(-50px) translateY(50vh);
    }
}

/* 交互动画 */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes heartbeat {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 展开动画 */
@keyframes expandIn {
    from {
        max-height: 0;
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        max-height: 1000px;
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes collapseOut {
    from {
        max-height: 1000px;
        opacity: 1;
        transform: translateY(0);
    }
    to {
        max-height: 0;
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* 应用动画类 */
.fade-in {
    animation: fadeIn 1s ease-out;
}

.slide-up {
    animation: slideUp 0.8s ease-out;
}

.glow-text {
    animation: glow 3s ease-in-out infinite;
}

.typewriter-text {
    overflow: hidden;
    white-space: nowrap;
    animation: typewriter 3s steps(40, end);
}

/* 装饰元素动画应用 */
.petal-1 {
    left: 10%;
    animation: petalFall1 8s linear infinite;
    animation-delay: 0s;
}

.petal-2 {
    left: 30%;
    animation: petalFall2 10s linear infinite;
    animation-delay: 2s;
}

.petal-3 {
    left: 50%;
    animation: petalFall3 9s linear infinite;
    animation-delay: 4s;
}

.petal-4 {
    left: 70%;
    animation: petalFall1 11s linear infinite;
    animation-delay: 1s;
}

.petal-5 {
    left: 85%;
    animation: petalFall2 7s linear infinite;
    animation-delay: 3s;
}

.petal-6 {
    left: 95%;
    animation: petalFall3 12s linear infinite;
    animation-delay: 5s;
}

.star-1 {
    top: 20%;
    left: 15%;
    animation: starTwinkle 2s ease-in-out infinite;
    animation-delay: 0s;
}

.star-2 {
    top: 30%;
    right: 20%;
    animation: starGlow 3s ease-in-out infinite;
    animation-delay: 1s;
}

.star-3 {
    top: 60%;
    left: 25%;
    animation: starTwinkle 2.5s ease-in-out infinite;
    animation-delay: 0.5s;
}

.star-4 {
    top: 70%;
    right: 30%;
    animation: starGlow 2.8s ease-in-out infinite;
    animation-delay: 1.5s;
}

.star-5 {
    top: 85%;
    left: 60%;
    animation: starTwinkle 3.2s ease-in-out infinite;
    animation-delay: 2s;
}

.butterfly-1 {
    animation: butterflyFly1 15s linear infinite;
    animation-delay: 0s;
}

.butterfly-2 {
    animation: butterflyFly2 18s linear infinite;
    animation-delay: 5s;
}

/* 动画控制类 */
.animations-paused * {
    animation-play-state: paused !important;
}

.animations-running * {
    animation-play-state: running !important;
}

/* 页面初始加载动画 */
.main-container {
    animation: fadeIn 1.5s ease-out;
}

.birthday-card {
    animation: slideUp 1s ease-out 0.5s both;
}

.control-panel {
    animation: fadeIn 1s ease-out 1s both;
}

/* 响应式动画调整 */
@media (max-width: 768px) {
    .petal-1, .petal-2, .petal-3, .petal-4, .petal-5, .petal-6 {
        font-size: 1rem;
    }
    
    .star-1, .star-2, .star-3, .star-4, .star-5 {
        font-size: 1rem;
    }
    
    .butterfly-1, .butterfly-2 {
        font-size: 1.2rem;
    }
}

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
    .petal-1, .petal-2, .petal-3, .petal-4, .petal-5, .petal-6,
    .star-1, .star-2, .star-3, .star-4, .star-5,
    .butterfly-1, .butterfly-2 {
        animation: none;
    }
    
    .main-container, .birthday-card, .control-panel {
        animation: none;
    }
}
