// 分享功能模块 - 让用户轻松分享生日贺卡
// 为董双拿女士的生日贺卡提供多种分享方式

class ShareController {
    constructor() {
        this.shareData = {
            title: '生日快乐 - 董双拿女士',
            text: '为董双拿女士精心制作的生日贺卡，愿您生日快乐，身体健康！🎂🎉',
            url: window.location.href
        };
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkShareSupport();
        this.setupSocialSharing();
    }

    // 绑定事件
    bindEvents() {
        const shareBtn = document.getElementById('shareBtn');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => this.handleShare());
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                this.handleShare();
            }
        });
    }

    // 检查分享支持
    checkShareSupport() {
        this.supportsWebShare = 'share' in navigator;
        this.supportsClipboard = 'clipboard' in navigator;
        
        console.log('分享功能支持:', {
            webShare: this.supportsWebShare,
            clipboard: this.supportsClipboard
        });
    }

    // 处理分享
    async handleShare() {
        try {
            if (this.supportsWebShare) {
                await this.webShare();
            } else {
                this.showShareModal();
            }
        } catch (error) {
            console.error('分享失败:', error);
            this.showShareModal();
        }
    }

    // Web Share API 分享
    async webShare() {
        try {
            await navigator.share(this.shareData);
            console.log('分享成功');
            this.showShareSuccess();
        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('用户取消分享');
            } else {
                throw error;
            }
        }
    }

    // 显示分享模态框
    showShareModal() {
        // 创建模态框
        const modal = this.createShareModal();
        document.body.appendChild(modal);
        
        // 添加动画
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
        
        // 绑定关闭事件
        this.bindModalEvents(modal);
    }

    // 创建分享模态框
    createShareModal() {
        const modal = document.createElement('div');
        modal.className = 'share-modal';
        modal.innerHTML = `
            <div class="share-modal-overlay"></div>
            <div class="share-modal-content">
                <div class="share-modal-header">
                    <h3>分享生日贺卡</h3>
                    <button class="share-modal-close" aria-label="关闭">&times;</button>
                </div>
                <div class="share-modal-body">
                    <div class="share-options">
                        <button class="share-option" data-method="copy">
                            <span class="share-icon">📋</span>
                            <span class="share-label">复制链接</span>
                        </button>
                        <button class="share-option" data-method="email">
                            <span class="share-icon">📧</span>
                            <span class="share-label">邮件分享</span>
                        </button>
                        <button class="share-option" data-method="wechat">
                            <span class="share-icon">💬</span>
                            <span class="share-label">微信分享</span>
                        </button>
                        <button class="share-option" data-method="weibo">
                            <span class="share-icon">🐦</span>
                            <span class="share-label">微博分享</span>
                        </button>
                        <button class="share-option" data-method="qq">
                            <span class="share-icon">🐧</span>
                            <span class="share-label">QQ分享</span>
                        </button>
                        <button class="share-option" data-method="download">
                            <span class="share-icon">💾</span>
                            <span class="share-label">保存图片</span>
                        </button>
                    </div>
                    <div class="share-url">
                        <input type="text" class="share-url-input" value="${this.shareData.url}" readonly>
                        <button class="share-url-copy">复制</button>
                    </div>
                </div>
            </div>
        `;
        
        this.addModalStyles();
        return modal;
    }

    // 添加模态框样式
    addModalStyles() {
        if (document.getElementById('share-modal-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'share-modal-styles';
        style.textContent = `
            .share-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10000;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }
            
            .share-modal.show {
                opacity: 1;
                visibility: visible;
            }
            
            .share-modal-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(5px);
            }
            
            .share-modal-content {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) scale(0.9);
                background: white;
                border-radius: 16px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow: hidden;
                transition: transform 0.3s ease;
            }
            
            .share-modal.show .share-modal-content {
                transform: translate(-50%, -50%) scale(1);
            }
            
            .share-modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px;
                border-bottom: 1px solid #eee;
                background: linear-gradient(135deg, #FFB6C1, #E6E6FA);
            }
            
            .share-modal-header h3 {
                margin: 0;
                color: #333;
                font-size: 1.2rem;
            }
            
            .share-modal-close {
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: #666;
                padding: 5px;
                border-radius: 50%;
                transition: background 0.2s ease;
            }
            
            .share-modal-close:hover {
                background: rgba(0, 0, 0, 0.1);
            }
            
            .share-modal-body {
                padding: 20px;
            }
            
            .share-options {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 15px;
                margin-bottom: 20px;
            }
            
            .share-option {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 15px 10px;
                border: 2px solid #FFB6C1;
                border-radius: 12px;
                background: white;
                cursor: pointer;
                transition: all 0.2s ease;
                font-family: inherit;
            }
            
            .share-option:hover {
                background: #FFB6C1;
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(255, 182, 193, 0.3);
            }
            
            .share-icon {
                font-size: 1.5rem;
                margin-bottom: 5px;
            }
            
            .share-label {
                font-size: 0.9rem;
                color: #333;
            }
            
            .share-url {
                display: flex;
                gap: 10px;
                align-items: center;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 8px;
            }
            
            .share-url-input {
                flex: 1;
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 6px;
                font-size: 0.9rem;
                background: white;
            }
            
            .share-url-copy {
                padding: 8px 16px;
                background: #FFD700;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 0.9rem;
                transition: background 0.2s ease;
            }
            
            .share-url-copy:hover {
                background: #FFC700;
            }
            
            @media (max-width: 480px) {
                .share-options {
                    grid-template-columns: repeat(2, 1fr);
                }
                
                .share-url {
                    flex-direction: column;
                    gap: 10px;
                }
                
                .share-url-input {
                    width: 100%;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 绑定模态框事件
    bindModalEvents(modal) {
        // 关闭按钮
        const closeBtn = modal.querySelector('.share-modal-close');
        const overlay = modal.querySelector('.share-modal-overlay');
        
        const closeModal = () => {
            modal.classList.remove('show');
            setTimeout(() => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            }, 300);
        };
        
        closeBtn.addEventListener('click', closeModal);
        overlay.addEventListener('click', closeModal);
        
        // ESC键关闭
        const handleKeydown = (e) => {
            if (e.key === 'Escape') {
                closeModal();
                document.removeEventListener('keydown', handleKeydown);
            }
        };
        document.addEventListener('keydown', handleKeydown);
        
        // 分享选项
        const shareOptions = modal.querySelectorAll('.share-option');
        shareOptions.forEach(option => {
            option.addEventListener('click', () => {
                const method = option.dataset.method;
                this.executeShareMethod(method);
                closeModal();
            });
        });
        
        // 复制链接按钮
        const copyBtn = modal.querySelector('.share-url-copy');
        copyBtn.addEventListener('click', () => {
            this.copyToClipboard(this.shareData.url);
        });
    }

    // 执行分享方法
    async executeShareMethod(method) {
        switch(method) {
            case 'copy':
                await this.copyToClipboard(this.shareData.url);
                break;
            case 'email':
                this.shareViaEmail();
                break;
            case 'wechat':
                this.shareViaWeChat();
                break;
            case 'weibo':
                this.shareViaWeibo();
                break;
            case 'qq':
                this.shareViaQQ();
                break;
            case 'download':
                await this.downloadAsImage();
                break;
        }
    }

    // 复制到剪贴板
    async copyToClipboard(text) {
        try {
            if (this.supportsClipboard) {
                await navigator.clipboard.writeText(text);
            } else {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
            }
            
            this.showShareSuccess('链接已复制到剪贴板');
        } catch (error) {
            console.error('复制失败:', error);
            this.showShareError('复制失败，请手动复制链接');
        }
    }

    // 邮件分享
    shareViaEmail() {
        const subject = encodeURIComponent(this.shareData.title);
        const body = encodeURIComponent(`${this.shareData.text}\n\n${this.shareData.url}`);
        const mailtoUrl = `mailto:?subject=${subject}&body=${body}`;
        window.open(mailtoUrl);
    }

    // 微信分享（显示二维码）
    shareViaWeChat() {
        this.showQRCode(this.shareData.url, '微信扫码分享');
    }

    // 微博分享
    shareViaWeibo() {
        const text = encodeURIComponent(this.shareData.text);
        const url = encodeURIComponent(this.shareData.url);
        const weiboUrl = `https://service.weibo.com/share/share.php?title=${text}&url=${url}`;
        window.open(weiboUrl, '_blank', 'width=600,height=400');
    }

    // QQ分享
    shareViaQQ() {
        const title = encodeURIComponent(this.shareData.title);
        const summary = encodeURIComponent(this.shareData.text);
        const url = encodeURIComponent(this.shareData.url);
        const qqUrl = `https://connect.qq.com/widget/shareqq/index.html?title=${title}&summary=${summary}&url=${url}`;
        window.open(qqUrl, '_blank', 'width=600,height=400');
    }

    // 显示二维码
    showQRCode(url, title) {
        // 这里可以集成二维码生成库
        alert(`${title}\n\n请在微信中打开以下链接：\n${url}`);
    }

    // 下载为图片
    async downloadAsImage() {
        try {
            // 使用html2canvas库将页面转换为图片
            if (typeof html2canvas !== 'undefined') {
                const canvas = await html2canvas(document.body);
                const link = document.createElement('a');
                link.download = '董双拿女士生日贺卡.png';
                link.href = canvas.toDataURL();
                link.click();
            } else {
                // 降级方案：提示用户截图
                alert('请使用浏览器的截图功能保存贺卡图片');
            }
        } catch (error) {
            console.error('下载图片失败:', error);
            this.showShareError('下载失败，请尝试截图保存');
        }
    }

    // 显示分享成功消息
    showShareSuccess(message = '分享成功') {
        this.showToast(message, 'success');
        
        // 触发庆祝动画
        if (window.animationController) {
            window.animationController.createSpecialEffect('celebration');
        }
    }

    // 显示分享错误消息
    showShareError(message = '分享失败') {
        this.showToast(message, 'error');
    }

    // 显示提示消息
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `share-toast share-toast-${type}`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
            color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            z-index: 10001;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            font-size: 0.9rem;
        `;
        
        document.body.appendChild(toast);
        
        // 动画显示
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);
        
        // 自动隐藏
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    // 设置社交分享
    setupSocialSharing() {
        // 设置Open Graph标签
        this.setMetaTags();
    }

    // 设置Meta标签
    setMetaTags() {
        const metaTags = [
            { property: 'og:title', content: this.shareData.title },
            { property: 'og:description', content: this.shareData.text },
            { property: 'og:url', content: this.shareData.url },
            { property: 'og:type', content: 'website' },
            { name: 'twitter:card', content: 'summary_large_image' },
            { name: 'twitter:title', content: this.shareData.title },
            { name: 'twitter:description', content: this.shareData.text }
        ];
        
        metaTags.forEach(tag => {
            let meta = document.querySelector(`meta[${tag.property ? 'property' : 'name'}="${tag.property || tag.name}"]`);
            if (!meta) {
                meta = document.createElement('meta');
                if (tag.property) {
                    meta.setAttribute('property', tag.property);
                } else {
                    meta.setAttribute('name', tag.name);
                }
                document.head.appendChild(meta);
            }
            meta.setAttribute('content', tag.content);
        });
    }
}

// 页面加载完成后初始化分享控制器
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.shareController = new ShareController();
        console.log('📤 分享控制器已成功加载！');
    } catch (error) {
        console.error('分享控制器初始化失败:', error);
    }
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ShareController;
}
