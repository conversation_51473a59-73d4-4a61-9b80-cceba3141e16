/* CSS变量定义 - 温馨色彩主题 */
:root {
    /* 主题色彩 */
    --primary-pink: #FFB6C1;
    --secondary-pink: #FFC0CB;
    --accent-gold: #FFD700;
    --warm-white: #FFFEF7;
    --soft-purple: #E6E6FA;
    --gentle-rose: #F0E68C;
    
    /* 渐变色 */
    --gradient-main: linear-gradient(135deg, var(--primary-pink) 0%, var(--soft-purple) 50%, var(--accent-gold) 100%);
    --gradient-card: linear-gradient(145deg, var(--warm-white) 0%, #FFF8DC 50%, var(--secondary-pink) 100%);
    --gradient-text: linear-gradient(45deg, var(--accent-gold), #FF69B4, var(--primary-pink));
    
    /* 字体大小 */
    --font-size-xl: clamp(2.5rem, 5vw, 4rem);
    --font-size-lg: clamp(1.5rem, 3vw, 2.5rem);
    --font-size-md: clamp(1rem, 2vw, 1.5rem);
    --font-size-sm: clamp(0.875rem, 1.5vw, 1.125rem);
    
    /* 间距 */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    
    /* 阴影 */
    --shadow-soft: 0 4px 20px rgba(255, 182, 193, 0.3);
    --shadow-medium: 0 8px 30px rgba(255, 182, 193, 0.4);
    --shadow-strong: 0 12px 40px rgba(255, 182, 193, 0.5);
    
    /* 圆角 */
    --border-radius-sm: 8px;
    --border-radius-md: 16px;
    --border-radius-lg: 24px;
    --border-radius-xl: 32px;
}

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Noto Serif SC', 'Ma Shan Zheng', serif;
    background: var(--gradient-main);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
    line-height: 1.6;
}

/* 装饰元素基础样式 */
.decorations {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.petals, .stars, .butterflies {
    position: absolute;
    width: 100%;
    height: 100%;
}

.petal, .star, .butterfly {
    position: absolute;
    font-size: var(--font-size-md);
    opacity: 0.8;
}

/* 主容器 */
.main-container {
    position: relative;
    z-index: 10;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
    gap: var(--spacing-lg);
}

/* 生日贺卡样式 */
.birthday-card {
    background: var(--gradient-card);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-strong);
    padding: var(--spacing-xl);
    max-width: 600px;
    width: 100%;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid transparent;
    background-clip: padding-box;
    position: relative;
    overflow: hidden;
}

.birthday-card::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: var(--gradient-main);
    border-radius: var(--border-radius-xl);
    z-index: -1;
}

.birthday-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: var(--shadow-strong), 0 0 30px rgba(255, 215, 0, 0.4);
}

.birthday-card:focus {
    outline: 3px solid var(--accent-gold);
    outline-offset: 4px;
}

/* 卡片标题 */
.card-header {
    margin-bottom: var(--spacing-lg);
}

.main-title {
    font-family: 'Ma Shan Zheng', cursive;
    font-size: var(--font-size-xl);
    background: var(--gradient-text);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-sm);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.title-line {
    display: block;
    margin-bottom: var(--spacing-xs);
}

.name-highlight {
    display: block;
    font-size: var(--font-size-lg);
    color: var(--accent-gold);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    font-weight: 600;
}

/* 卡片内容 */
.card-content {
    margin-bottom: var(--spacing-lg);
}

.blessing-text {
    font-size: var(--font-size-md);
    color: #333;
    line-height: 1.8;
    margin-bottom: var(--spacing-md);
}

.blessing-main {
    margin-bottom: var(--spacing-sm);
}

/* 详细祝福（初始隐藏） */
.detailed-blessing {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s ease, opacity 0.3s ease;
    opacity: 0;
    font-size: var(--font-size-sm);
    color: #444;
    line-height: 1.7;
}

.detailed-blessing.expanded {
    max-height: 1000px;
    opacity: 1;
    margin-top: var(--spacing-md);
}

.blessing-detail {
    text-align: left;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius-md);
    border-left: 4px solid var(--accent-gold);
}

/* 卡片底部 */
.card-footer {
    margin-top: var(--spacing-md);
}

.click-hint {
    font-size: var(--font-size-sm);
    color: var(--accent-gold);
    animation: pulse 2s infinite;
}

.click-hint.hidden {
    display: none;
}

/* 农历日期样式 */
.lunar-date {
    margin-top: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: linear-gradient(45deg, var(--accent-gold), #FFA500);
    border-radius: var(--border-radius-sm);
    display: inline-block;
    box-shadow: var(--shadow-soft);
}

.date-label {
    font-size: var(--font-size-sm);
    color: white;
    font-weight: 600;
    margin-right: var(--spacing-xs);
}

.date-value {
    font-size: var(--font-size-sm);
    color: white;
    font-weight: 400;
}

/* 控制面板 */
.control-panel {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    justify-content: center;
    align-items: center;
}

/* 按钮基础样式 */
.animation-btn {
    background: var(--gradient-card);
    border: 2px solid var(--accent-gold);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    box-shadow: var(--shadow-soft);
    font-family: inherit;
}

.animation-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    background: var(--accent-gold);
    color: white;
}

.animation-btn:active {
    transform: translateY(0);
}

/* 惊喜卡片区域 */
.surprise-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
    max-width: 700px;
    width: 100%;
}

/* 惊喜卡片样式 */
.surprise-card {
    position: relative;
    width: 100%;
    height: 160px;
    perspective: 1000px;
    cursor: pointer;
}

.card-front, .card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-soft);
    transition: transform 0.6s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
    text-align: center;
}

.card-front {
    background: var(--gradient-card);
    border: 2px solid var(--primary-pink);
}

.card-back {
    background: var(--gradient-main);
    color: white;
    transform: rotateY(180deg);
}

.surprise-card.flipped .card-front {
    transform: rotateY(-180deg);
}

.surprise-card.flipped .card-back {
    transform: rotateY(0deg);
}

.surprise-icon {
    font-size: 2rem;
    margin-bottom: var(--spacing-xs);
    animation: bounce 2s infinite;
}

.surprise-text {
    font-size: var(--font-size-sm);
    color: #333;
    font-weight: 500;
}

.card-back h4 {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
    color: var(--accent-gold);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    font-weight: 600;
}

.card-back p {
    font-size: 0.85rem;
    line-height: 1.4;
    color: white;
    text-align: center;
    overflow: hidden;
    word-wrap: break-word;
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-main);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    transition: opacity 0.5s ease;
}

.loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    font-size: 4rem;
    animation: spin 2s linear infinite;
    margin-bottom: var(--spacing-md);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        padding: var(--spacing-sm);
        gap: var(--spacing-md);
    }

    .birthday-card {
        padding: var(--spacing-lg);
        margin: var(--spacing-sm);
    }

    .control-panel {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .animation-controls {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .surprise-cards {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-sm);
    }

    .surprise-card {
        height: 140px;
    }

    .card-front, .card-back {
        padding: var(--spacing-sm);
    }

    .surprise-icon {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .birthday-card {
        padding: var(--spacing-md);
    }

    .main-title {
        font-size: var(--font-size-lg);
    }

    .blessing-text {
        font-size: var(--font-size-sm);
    }

    .surprise-cards {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
        max-width: 300px;
    }

    .surprise-card {
        height: 120px;
    }

    .card-back h4 {
        font-size: 0.9rem;
        margin-bottom: 4px;
    }

    .card-back p {
        font-size: 0.75rem;
        line-height: 1.3;
    }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .birthday-card {
        border: 3px solid #000;
    }

    .main-title {
        color: #000;
        -webkit-text-fill-color: #000;
    }
}

/* 音乐按钮播放状态 */
.music-btn.playing {
    background: var(--accent-gold);
    color: white;
    animation: pulse 2s infinite;
}

.music-btn.playing .music-icon {
    animation: bounce 1s infinite;
}

/* 贺卡展开状态 */
.birthday-card.expanded {
    transform: scale(1.02);
    box-shadow: var(--shadow-strong), 0 0 40px rgba(255, 215, 0, 0.6);
}

/* 心跳动画 */
.heartbeat {
    animation: heartbeat 0.6s ease-in-out;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}
