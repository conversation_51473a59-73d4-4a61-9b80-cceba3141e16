// 动画控制模块 - 专门处理生日贺卡的动画效果
// 为董双拿女士的生日贺卡提供流畅的动画体验

class AnimationController {
    constructor() {
        this.animationState = {
            petals: true,
            stars: true,
            butterflies: true,
            cardAnimations: true
        };
        this.observers = [];
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
        this.createDynamicDecorations();
        this.setupPerformanceMonitoring();
    }

    // 设置交叉观察器用于性能优化
    setupIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.startElementAnimations(entry.target);
                    } else {
                        this.pauseElementAnimations(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });

            // 观察所有动画元素
            const animatedElements = document.querySelectorAll('.petal, .star, .butterfly');
            animatedElements.forEach(el => this.observer.observe(el));
        }
    }

    // 动态创建装饰元素
    createDynamicDecorations() {
        this.createAdditionalPetals();
        this.createFloatingHearts();
        this.createSparkles();
    }

    // 创建额外的花瓣
    createAdditionalPetals() {
        const petalsContainer = document.querySelector('.petals');
        if (!petalsContainer) return;

        const petalEmojis = ['🌸', '🌺', '🌻', '🌷', '🌹', '💐'];
        
        for (let i = 7; i <= 12; i++) {
            const petal = document.createElement('div');
            petal.className = `petal petal-${i}`;
            petal.textContent = petalEmojis[Math.floor(Math.random() * petalEmojis.length)];
            
            // 随机位置和动画延迟
            petal.style.left = `${Math.random() * 100}%`;
            petal.style.animationDelay = `${Math.random() * 10}s`;
            petal.style.animationDuration = `${8 + Math.random() * 6}s`;
            
            // 随机选择动画类型
            const animationTypes = ['petalFall1', 'petalFall2', 'petalFall3'];
            const randomAnimation = animationTypes[Math.floor(Math.random() * animationTypes.length)];
            petal.style.animationName = randomAnimation;
            
            petalsContainer.appendChild(petal);
        }
    }

    // 创建飘浮的心形
    createFloatingHearts() {
        const heartsContainer = document.createElement('div');
        heartsContainer.className = 'floating-hearts';
        document.querySelector('.decorations').appendChild(heartsContainer);

        const heartEmojis = ['💖', '💕', '💗', '💝', '💘'];
        
        for (let i = 0; i < 5; i++) {
            const heart = document.createElement('div');
            heart.className = `floating-heart heart-${i + 1}`;
            heart.textContent = heartEmojis[i];
            heart.style.left = `${20 + i * 15}%`;
            heart.style.animationDelay = `${i * 2}s`;
            heartsContainer.appendChild(heart);
        }

        // 添加心形动画CSS
        this.addHeartAnimationStyles();
    }

    // 创建闪烁效果
    createSparkles() {
        const sparklesContainer = document.createElement('div');
        sparklesContainer.className = 'sparkles';
        document.querySelector('.decorations').appendChild(sparklesContainer);

        for (let i = 0; i < 8; i++) {
            const sparkle = document.createElement('div');
            sparkle.className = `sparkle sparkle-${i + 1}`;
            sparkle.textContent = '✨';
            sparkle.style.left = `${Math.random() * 100}%`;
            sparkle.style.top = `${Math.random() * 100}%`;
            sparkle.style.animationDelay = `${Math.random() * 3}s`;
            sparklesContainer.appendChild(sparkle);
        }

        // 添加闪烁动画CSS
        this.addSparkleAnimationStyles();
    }

    // 添加心形动画样式
    addHeartAnimationStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .floating-hearts {
                position: absolute;
                width: 100%;
                height: 100%;
                pointer-events: none;
            }
            
            .floating-heart {
                position: absolute;
                font-size: 1.5rem;
                opacity: 0.7;
                animation: floatHeart 6s ease-in-out infinite;
            }
            
            @keyframes floatHeart {
                0%, 100% {
                    transform: translateY(0) scale(1);
                    opacity: 0.7;
                }
                50% {
                    transform: translateY(-30px) scale(1.1);
                    opacity: 1;
                }
            }
            
            .heart-1 { top: 20%; animation-duration: 5s; }
            .heart-2 { top: 40%; animation-duration: 6s; }
            .heart-3 { top: 60%; animation-duration: 7s; }
            .heart-4 { top: 80%; animation-duration: 5.5s; }
            .heart-5 { top: 30%; animation-duration: 6.5s; }
        `;
        document.head.appendChild(style);
    }

    // 添加闪烁动画样式
    addSparkleAnimationStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .sparkles {
                position: absolute;
                width: 100%;
                height: 100%;
                pointer-events: none;
            }
            
            .sparkle {
                position: absolute;
                font-size: 1rem;
                animation: sparkleEffect 3s ease-in-out infinite;
            }
            
            @keyframes sparkleEffect {
                0%, 100% {
                    opacity: 0;
                    transform: scale(0.5) rotate(0deg);
                }
                50% {
                    opacity: 1;
                    transform: scale(1.2) rotate(180deg);
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 启动元素动画
    startElementAnimations(element) {
        element.style.animationPlayState = 'running';
    }

    // 暂停元素动画
    pauseElementAnimations(element) {
        element.style.animationPlayState = 'paused';
    }

    // 性能监控
    setupPerformanceMonitoring() {
        if ('requestIdleCallback' in window) {
            requestIdleCallback(() => {
                this.monitorFrameRate();
            });
        }
    }

    // 监控帧率
    monitorFrameRate() {
        let lastTime = performance.now();
        let frameCount = 0;
        
        const checkFrameRate = (currentTime) => {
            frameCount++;
            
            if (currentTime - lastTime >= 1000) {
                const fps = frameCount;
                frameCount = 0;
                lastTime = currentTime;
                
                // 如果帧率过低，自动降低动画复杂度
                if (fps < 30) {
                    this.reduceAnimationComplexity();
                }
            }
            
            requestAnimationFrame(checkFrameRate);
        };
        
        requestAnimationFrame(checkFrameRate);
    }

    // 降低动画复杂度
    reduceAnimationComplexity() {
        console.log('检测到性能问题，正在优化动画...');
        
        // 减少装饰元素数量
        const extraPetals = document.querySelectorAll('.petal:nth-child(n+7)');
        extraPetals.forEach(petal => {
            petal.style.display = 'none';
        });
        
        // 简化动画效果
        const allAnimatedElements = document.querySelectorAll('.petal, .star, .butterfly');
        allAnimatedElements.forEach(el => {
            el.style.animationDuration = '20s'; // 减慢动画速度
        });
    }

    // 创建特殊效果
    createSpecialEffect(type, x, y) {
        switch(type) {
            case 'click':
                this.createClickEffect(x, y);
                break;
            case 'celebration':
                this.createCelebrationEffect();
                break;
        }
    }

    // 点击特效
    createClickEffect(x, y) {
        const effect = document.createElement('div');
        effect.className = 'click-effect';
        effect.style.cssText = `
            position: fixed;
            left: ${x}px;
            top: ${y}px;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, #FFD700, transparent);
            border-radius: 50%;
            pointer-events: none;
            z-index: 1000;
            animation: clickExpand 0.6s ease-out forwards;
        `;
        
        document.body.appendChild(effect);
        
        setTimeout(() => {
            if (effect.parentNode) {
                effect.parentNode.removeChild(effect);
            }
        }, 600);
    }

    // 庆祝特效
    createCelebrationEffect() {
        const celebrationContainer = document.createElement('div');
        celebrationContainer.className = 'celebration-effect';
        celebrationContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 999;
        `;
        
        // 创建彩带效果
        for (let i = 0; i < 20; i++) {
            const confetti = document.createElement('div');
            confetti.textContent = ['🎉', '🎊', '🎈', '🎁'][Math.floor(Math.random() * 4)];
            confetti.style.cssText = `
                position: absolute;
                left: ${Math.random() * 100}%;
                top: -50px;
                font-size: 2rem;
                animation: confettiFall ${2 + Math.random() * 3}s linear forwards;
                animation-delay: ${Math.random() * 2}s;
            `;
            celebrationContainer.appendChild(confetti);
        }
        
        document.body.appendChild(celebrationContainer);
        
        // 5秒后移除庆祝效果
        setTimeout(() => {
            if (celebrationContainer.parentNode) {
                celebrationContainer.parentNode.removeChild(celebrationContainer);
            }
        }, 5000);
        
        // 添加彩带动画
        this.addConfettiAnimation();
    }

    // 添加彩带动画
    addConfettiAnimation() {
        if (document.getElementById('confetti-style')) return;
        
        const style = document.createElement('style');
        style.id = 'confetti-style';
        style.textContent = `
            @keyframes confettiFall {
                to {
                    transform: translateY(100vh) rotate(720deg);
                    opacity: 0;
                }
            }
            
            @keyframes clickExpand {
                from {
                    transform: scale(0);
                    opacity: 1;
                }
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 暂停所有动画
    pauseAllAnimations() {
        document.body.classList.add('animations-paused');
    }

    // 恢复所有动画
    resumeAllAnimations() {
        document.body.classList.remove('animations-paused');
        document.body.classList.add('animations-running');
    }

    // 清理资源
    cleanup() {
        if (this.observer) {
            this.observer.disconnect();
        }
        
        // 移除动态创建的元素
        const dynamicElements = document.querySelectorAll('.floating-hearts, .sparkles, .celebration-effect');
        dynamicElements.forEach(el => {
            if (el.parentNode) {
                el.parentNode.removeChild(el);
            }
        });
    }
}

// 页面加载完成后初始化动画控制器
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.animationController = new AnimationController();
        console.log('🎨 动画控制器已成功加载！');
    } catch (error) {
        console.error('动画控制器初始化失败:', error);
    }
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AnimationController;
}
